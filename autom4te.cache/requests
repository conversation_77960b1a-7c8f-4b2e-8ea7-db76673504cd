# This file was generated by Autom4te 2.72.
# It contains the lists of macros which have been traced.
# It can be safely removed.

@request = (
             bless( [
                      '0',
                      1,
                      [
                        '/usr/share/autoconf'
                      ],
                      [
                        '/usr/share/autoconf/autoconf/autoconf.m4f',
                        '/usr/share/aclocal-1.18/internal/ac-config-macro-dirs.m4',
                        '/usr/share/aclocal-1.18/amversion.m4',
                        '/usr/share/aclocal-1.18/auxdir.m4',
                        '/usr/share/aclocal-1.18/cond.m4',
                        '/usr/share/aclocal-1.18/depend.m4',
                        '/usr/share/aclocal-1.18/depout.m4',
                        '/usr/share/aclocal-1.18/init.m4',
                        '/usr/share/aclocal-1.18/install-sh.m4',
                        '/usr/share/aclocal-1.18/lead-dot.m4',
                        '/usr/share/aclocal-1.18/make.m4',
                        '/usr/share/aclocal-1.18/missing.m4',
                        '/usr/share/aclocal-1.18/options.m4',
                        '/usr/share/aclocal-1.18/prog-cc-c-o.m4',
                        '/usr/share/aclocal-1.18/rmf.m4',
                        '/usr/share/aclocal-1.18/runlog.m4',
                        '/usr/share/aclocal-1.18/sanity.m4',
                        '/usr/share/aclocal-1.18/silent.m4',
                        '/usr/share/aclocal-1.18/strip.m4',
                        '/usr/share/aclocal-1.18/substnot.m4',
                        '/usr/share/aclocal-1.18/tar.m4',
                        '/usr/share/aclocal-1.18/xargsn.m4',
                        'configure.ac'
                      ],
                      {
                        'AC_CONFIG_MACRO_DIR' => 1,
                        'AC_CONFIG_MACRO_DIR_TRACE' => 1,
                        'AC_DEFUN' => 1,
                        'AC_DEFUN_ONCE' => 1,
                        'AM_AUTOMAKE_VERSION' => 1,
                        'AM_AUX_DIR_EXPAND' => 1,
                        'AM_CONDITIONAL' => 1,
                        'AM_DEP_TRACK' => 1,
                        'AM_INIT_AUTOMAKE' => 1,
                        'AM_MAKE_INCLUDE' => 1,
                        'AM_MISSING_HAS_RUN' => 1,
                        'AM_MISSING_PROG' => 1,
                        'AM_OUTPUT_DEPENDENCY_COMMANDS' => 1,
                        'AM_PROG_CC_C_O' => 1,
                        'AM_PROG_INSTALL_SH' => 1,
                        'AM_PROG_INSTALL_STRIP' => 1,
                        'AM_RUN_LOG' => 1,
                        'AM_SANITY_CHECK' => 1,
                        'AM_SET_CURRENT_AUTOMAKE_VERSION' => 1,
                        'AM_SET_DEPDIR' => 1,
                        'AM_SET_LEADING_DOT' => 1,
                        'AM_SILENT_RULES' => 1,
                        'AM_SUBST_NOTMAKE' => 1,
                        'AU_DEFUN' => 1,
                        '_AC_AM_CONFIG_HEADER_HOOK' => 1,
                        '_AM_AUTOCONF_VERSION' => 1,
                        '_AM_CONFIG_MACRO_DIRS' => 1,
                        '_AM_DEPENDENCIES' => 1,
                        '_AM_FILESYSTEM_TIMESTAMP_RESOLUTION' => 1,
                        '_AM_IF_OPTION' => 1,
                        '_AM_MANGLE_OPTION' => 1,
                        '_AM_OUTPUT_DEPENDENCY_COMMANDS' => 1,
                        '_AM_PROG_CC_C_O' => 1,
                        '_AM_PROG_RM_F' => 1,
                        '_AM_PROG_TAR' => 1,
                        '_AM_PROG_XARGS_N' => 1,
                        '_AM_SET_OPTION' => 1,
                        '_AM_SET_OPTIONS' => 1,
                        '_AM_SILENT_RULES' => 1,
                        '_AM_SLEEP_FRACTIONAL_SECONDS' => 1,
                        '_AM_SUBST_NOTMAKE' => 1,
                        '_m4_warn' => 1,
                        'include' => 1,
                        'm4_include' => 1,
                        'm4_pattern_allow' => 1,
                        'm4_pattern_forbid' => 1
                      }
                    ], 'Autom4te::Request' ),
             bless( [
                      '1',
                      1,
                      [
                        '/usr/share/autoconf'
                      ],
                      [
                        '/usr/share/autoconf/autoconf/autoconf.m4f',
                        'aclocal.m4',
                        'configure.ac'
                      ],
                      {
                        'AC_CANONICAL_BUILD' => 1,
                        'AC_CANONICAL_HOST' => 1,
                        'AC_CANONICAL_SYSTEM' => 1,
                        'AC_CANONICAL_TARGET' => 1,
                        'AC_CONFIG_AUX_DIR' => 1,
                        'AC_CONFIG_FILES' => 1,
                        'AC_CONFIG_HEADERS' => 1,
                        'AC_CONFIG_LIBOBJ_DIR' => 1,
                        'AC_CONFIG_LINKS' => 1,
                        'AC_CONFIG_MACRO_DIR_TRACE' => 1,
                        'AC_CONFIG_SUBDIRS' => 1,
                        'AC_DEFINE_TRACE_LITERAL' => 1,
                        'AC_FC_FREEFORM' => 1,
                        'AC_FC_PP_DEFINE' => 1,
                        'AC_FC_PP_SRCEXT' => 1,
                        'AC_FC_SRCEXT' => 1,
                        'AC_INIT' => 1,
                        'AC_LIBSOURCE' => 1,
                        'AC_PROG_LIBTOOL' => 1,
                        'AC_REQUIRE_AUX_FILE' => 1,
                        'AC_SUBST' => 1,
                        'AC_SUBST_TRACE' => 1,
                        'AH_OUTPUT' => 1,
                        'AM_AUTOMAKE_VERSION' => 1,
                        'AM_CONDITIONAL' => 1,
                        'AM_ENABLE_MULTILIB' => 1,
                        'AM_EXTRA_RECURSIVE_TARGETS' => 1,
                        'AM_GNU_GETTEXT' => 1,
                        'AM_GNU_GETTEXT_INTL_SUBDIR' => 1,
                        'AM_INIT_AUTOMAKE' => 1,
                        'AM_MAINTAINER_MODE' => 1,
                        'AM_MAKEFILE_INCLUDE' => 1,
                        'AM_NLS' => 1,
                        'AM_PATH_GUILE' => 1,
                        'AM_POT_TOOLS' => 1,
                        'AM_PROG_AR' => 1,
                        'AM_PROG_CC_C_O' => 1,
                        'AM_PROG_CXX_C_O' => 1,
                        'AM_PROG_F77_C_O' => 1,
                        'AM_PROG_FC_C_O' => 1,
                        'AM_PROG_LIBTOOL' => 1,
                        'AM_PROG_MKDIR_P' => 1,
                        'AM_PROG_MOC' => 1,
                        'AM_SILENT_RULES' => 1,
                        'AM_XGETTEXT_OPTION' => 1,
                        'GTK_DOC_CHECK' => 1,
                        'IT_PROG_INTLTOOL' => 1,
                        'LT_CONFIG_LTDL_DIR' => 1,
                        'LT_INIT' => 1,
                        'LT_SUPPORTED_TAG' => 1,
                        '_AM_COND_ELSE' => 1,
                        '_AM_COND_ENDIF' => 1,
                        '_AM_COND_IF' => 1,
                        '_AM_MAKEFILE_INCLUDE' => 1,
                        '_AM_SUBST_NOTMAKE' => 1,
                        '_LT_AC_TAGCONFIG' => 1,
                        '_m4_warn' => 1,
                        'include' => 1,
                        'm4_include' => 1,
                        'm4_pattern_allow' => 1,
                        'm4_pattern_forbid' => 1,
                        'm4_sinclude' => 1,
                        'sinclude' => 1
                      }
                    ], 'Autom4te::Request' ),
             bless( [
                      '2',
                      1,
                      [
                        '/usr/share/autoconf'
                      ],
                      [
                        '/usr/share/autoconf/autoconf/autoconf.m4f',
                        'aclocal.m4',
                        '/usr/share/autoconf/autoconf/trailer.m4',
                        'configure.ac'
                      ],
                      {
                        'AC_CANONICAL_BUILD' => 1,
                        'AC_CANONICAL_HOST' => 1,
                        'AC_CANONICAL_SYSTEM' => 1,
                        'AC_CANONICAL_TARGET' => 1,
                        'AC_CONFIG_AUX_DIR' => 1,
                        'AC_CONFIG_FILES' => 1,
                        'AC_CONFIG_HEADERS' => 1,
                        'AC_CONFIG_LIBOBJ_DIR' => 1,
                        'AC_CONFIG_LINKS' => 1,
                        'AC_CONFIG_MACRO_DIR_TRACE' => 1,
                        'AC_CONFIG_SUBDIRS' => 1,
                        'AC_DEFINE_TRACE_LITERAL' => 1,
                        'AC_FC_FREEFORM' => 1,
                        'AC_FC_PP_DEFINE' => 1,
                        'AC_FC_PP_SRCEXT' => 1,
                        'AC_FC_SRCEXT' => 1,
                        'AC_INIT' => 1,
                        'AC_LIBSOURCE' => 1,
                        'AC_PROG_LIBTOOL' => 1,
                        'AC_REQUIRE_AUX_FILE' => 1,
                        'AC_SUBST' => 1,
                        'AC_SUBST_TRACE' => 1,
                        'AH_OUTPUT' => 1,
                        'AM_AUTOMAKE_VERSION' => 1,
                        'AM_CONDITIONAL' => 1,
                        'AM_ENABLE_MULTILIB' => 1,
                        'AM_EXTRA_RECURSIVE_TARGETS' => 1,
                        'AM_GNU_GETTEXT' => 1,
                        'AM_GNU_GETTEXT_INTL_SUBDIR' => 1,
                        'AM_INIT_AUTOMAKE' => 1,
                        'AM_MAINTAINER_MODE' => 1,
                        'AM_MAKEFILE_INCLUDE' => 1,
                        'AM_NLS' => 1,
                        'AM_PATH_GUILE' => 1,
                        'AM_POT_TOOLS' => 1,
                        'AM_PROG_AR' => 1,
                        'AM_PROG_CC_C_O' => 1,
                        'AM_PROG_CXX_C_O' => 1,
                        'AM_PROG_F77_C_O' => 1,
                        'AM_PROG_FC_C_O' => 1,
                        'AM_PROG_LIBTOOL' => 1,
                        'AM_PROG_MKDIR_P' => 1,
                        'AM_PROG_MOC' => 1,
                        'AM_SILENT_RULES' => 1,
                        'AM_XGETTEXT_OPTION' => 1,
                        'GTK_DOC_CHECK' => 1,
                        'IT_PROG_INTLTOOL' => 1,
                        'LT_CONFIG_LTDL_DIR' => 1,
                        'LT_INIT' => 1,
                        'LT_SUPPORTED_TAG' => 1,
                        '_AM_COND_ELSE' => 1,
                        '_AM_COND_ENDIF' => 1,
                        '_AM_COND_IF' => 1,
                        '_AM_MAKEFILE_INCLUDE' => 1,
                        '_AM_SUBST_NOTMAKE' => 1,
                        '_LT_AC_TAGCONFIG' => 1,
                        '_m4_warn' => 1,
                        'include' => 1,
                        'm4_include' => 1,
                        'm4_pattern_allow' => 1,
                        'm4_pattern_forbid' => 1,
                        'm4_sinclude' => 1,
                        'sinclude' => 1
                      }
                    ], 'Autom4te::Request' )
           );

