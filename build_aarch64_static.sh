#!/bin/bash

# bsdiff aarch64 静态编译脚本
# 使用交叉编译器: gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu

set -e  # 遇到错误立即退出

# 配置变量
CROSS_COMPILER_PATH="/home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu"
CC="${CROSS_COMPILER_PATH}/bin/aarch64-linux-gnu-gcc"
AR="${CROSS_COMPILER_PATH}/bin/aarch64-linux-gnu-ar"
RANLIB="${CROSS_COMPILER_PATH}/bin/aarch64-linux-gnu-ranlib"
BZIP2_VERSION="1.0.8"
BZIP2_DIR="bzip2-${BZIP2_VERSION}"

echo "=== bsdiff aarch64 静态编译脚本 ==="
echo "交叉编译器: ${CC}"
echo "目标架构: ARM aarch64"
echo ""

# 检查交叉编译器是否存在
if [ ! -f "${CC}" ]; then
    echo "错误: 交叉编译器不存在: ${CC}"
    exit 1
fi

echo "✓ 交叉编译器检查通过"

# 清理之前的编译结果
echo "清理之前的编译结果..."
make clean 2>/dev/null || true
if [ -d "${BZIP2_DIR}" ]; then
    cd "${BZIP2_DIR}"
    make clean 2>/dev/null || true
    cd ..
fi

# 下载并编译bzip2静态库（如果不存在）
if [ ! -f "${BZIP2_DIR}/libbz2.a" ]; then
    echo "编译bzip2静态库..."
    
    if [ ! -d "${BZIP2_DIR}" ]; then
        if [ ! -f "bzip2-${BZIP2_VERSION}.tar.gz" ]; then
            echo "下载bzip2源码..."
            wget "https://sourceware.org/pub/bzip2/bzip2-${BZIP2_VERSION}.tar.gz"
        fi
        tar -xzf "bzip2-${BZIP2_VERSION}.tar.gz"
    fi
    
    cd "${BZIP2_DIR}"
    make CC="${CC}" AR="${AR}" RANLIB="${RANLIB}" libbz2.a
    cd ..
    echo "✓ bzip2静态库编译完成"
else
    echo "✓ bzip2静态库已存在"
fi

# 配置bsdiff
echo "配置bsdiff..."
./configure \
    CC="${CC}" \
    CFLAGS="-static -I./${BZIP2_DIR}" \
    LDFLAGS="-static -L./${BZIP2_DIR}" \
    --host=aarch64-linux-gnu

echo "✓ 配置完成"

# 编译bsdiff
echo "编译bsdiff..."
make

echo "✓ 编译完成"

# 验证结果
echo ""
echo "=== 编译结果验证 ==="
echo "二进制文件:"
ls -lh bsdiff bspatch

echo ""
echo "架构信息:"
file bsdiff bspatch

echo ""
echo "=== 编译成功! ==="
echo "生成的文件:"
echo "  - bsdiff: $(du -h bsdiff | cut -f1)"
echo "  - bspatch: $(du -h bspatch | cut -f1)"
echo ""
echo "这些文件可以在aarch64 Linux系统上运行，无需任何外部依赖。"
