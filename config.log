This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by bsdiff configure 0.1, which was
generated by GNU Autoconf 2.72.  Invocation command line was

  $ ./configure CC=/home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc 'CFLAGS=-static -I./bzip2-1.0.8' 'LDFLAGS=-static -L./bzip2-1.0.8' --host=aarch64-linux-gnu

## --------- ##
## Platform. ##
## --------- ##

hostname = archlinuxg
uname -m = x86_64
uname -r = 6.15.1-arch1-2
uname -s = Linux
uname -v = #1 SMP PREEMPT_DYNAMIC Sat, 07 Jun 2025 14:36:24 +0000

/usr/bin/uname -p = unknown
/bin/uname -X     = unknown

/bin/arch              = unknown
/usr/bin/arch -k       = unknown
/usr/convex/getsysinfo = unknown
/usr/bin/hostinfo      = unknown
/bin/machine           = unknown
/usr/bin/oslevel       = unknown
/bin/universe          = unknown

PATH: /home/<USER>/.vscode-server/cli/servers/Stable-c306e94f98122556ca081f527b466015e1bc37b0/server/bin/remote-cli/
PATH: /home/<USER>/.cargo/bin/
PATH: /usr/local/sbin/
PATH: /usr/local/bin/
PATH: /usr/bin/
PATH: /home/<USER>/.local/share/flatpak/exports/bin/
PATH: /var/lib/flatpak/exports/bin/
PATH: /usr/lib/jvm/default/bin/
PATH: /usr/bin/site_perl/
PATH: /usr/bin/vendor_perl/
PATH: /usr/bin/core_perl/
PATH: /usr/lib/rustup/bin/
PATH: /opt/cross_compiler/gcc-sigmastar-9.1.0-2020.07-x86_64_arm-linux-gnueabihf/bin/
PATH: /opt/cross_compiler/android-ndk-r15c-toolchain64/bin/
PATH: /home/<USER>/.c1/
PATH: /home/<USER>/.tools/
PATH: /home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand/


## ----------- ##
## Core tests. ##
## ----------- ##

configure:2553: looking for aux files: config.guess config.sub compile missing install-sh
configure:2566:  trying ./
configure:2595:   ./config.guess found
configure:2595:   ./config.sub found
configure:2595:   ./compile found
configure:2595:   ./missing found
configure:2577:   ./install-sh found
configure:2728: checking for a BSD-compatible install
configure:2802: result: /usr/bin/install -c
configure:2813: checking whether sleep supports fractional seconds
configure:2829: result: yes
configure:2832: checking filesystem timestamp resolution
configure:2967: result: 0.01
configure:2972: checking whether build environment is sane
configure:3017: result: yes
configure:3083: checking for aarch64-linux-gnu-strip
configure:3119: result: no
configure:3129: checking for strip
configure:3150: found /usr/bin/strip
configure:3162: result: strip
configure:3188: checking for a race-free mkdir -p
configure:3231: result: /usr/bin/mkdir -p
configure:3238: checking for gawk
configure:3259: found /usr/bin/gawk
configure:3271: result: gawk
configure:3282: checking whether make sets $(MAKE)
configure:3306: result: yes
configure:3332: checking whether make supports nested variables
configure:3351: result: yes
configure:3365: checking xargs -n works
configure:3381: result: yes
configure:3466: checking whether UID '1000' is supported by ustar format
configure:3472: result: yes
configure:3479: checking whether GID '1000' is supported by ustar format
configure:3485: result: yes
configure:3493: checking how to create a ustar tar archive
configure:3504: tar --version
tar (GNU tar) 1.35
Copyright (C) 2023 Free Software Foundation, Inc.
License GPLv3+: GNU GPL version 3 or later <https://gnu.org/licenses/gpl.html>.
This is free software: you are free to change and redistribute it.
There is NO WARRANTY, to the extent permitted by law.

Written by John Gilmore and Jay Fenlason.
configure:3507: $? = 0
configure:3547: tardir=conftest.dir && eval tar --format=ustar -chf - "$tardir" >conftest.tar
configure:3550: $? = 0
configure:3554: tar -xf - <conftest.tar
configure:3557: $? = 0
configure:3559: cat conftest.dir/file
GrepMe
configure:3562: $? = 0
configure:3577: result: gnutar
configure:3622: checking for aarch64-linux-gnu-gcc
configure:3655: result: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc
configure:4060: checking for C compiler version
configure:4069: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc --version >&5
aarch64-linux-gnu-gcc (Linaro GCC 6.5-2018.12) 6.5.0
Copyright (C) 2017 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:4080: $? = 0
configure:4069: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -v >&5
Using built-in specs.
COLLECT_GCC=/home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc
COLLECT_LTO_WRAPPER=/home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/../libexec/gcc/aarch64-linux-gnu/6.5.0/lto-wrapper
Target: aarch64-linux-gnu
Configured with: '/home/<USER>/workspace/tcwg-make-release_1/snapshots/gcc.git~linaro-6.5-2018.12/configure' SHELL=/bin/bash --with-mpc=/home/<USER>/workspace/tcwg-make-release_1/_build/builds/destdir/x86_64-unknown-linux-gnu --with-mpfr=/home/<USER>/workspace/tcwg-make-release_1/_build/builds/destdir/x86_64-unknown-linux-gnu --with-gmp=/home/<USER>/workspace/tcwg-make-release_1/_build/builds/destdir/x86_64-unknown-linux-gnu --with-gnu-as --with-gnu-ld --disable-libmudflap --enable-lto --enable-shared --without-included-gettext --enable-nls --with-system-zlib --disable-sjlj-exceptions --enable-gnu-unique-object --enable-linker-build-id --disable-libstdcxx-pch --enable-c99 --enable-clocale=gnu --enable-libstdcxx-debug --enable-long-long --with-cloog=no --with-ppl=no --with-isl=no --disable-multilib --enable-fix-cortex-a53-835769 --enable-fix-cortex-a53-843419 --with-arch=armv8-a --enable-threads=posix --enable-multiarch --enable-libstdcxx-time=yes --enable-gnu-indirect-function --with-build-sysroot=/home/<USER>/workspace/tcwg-make-release_1/_build/sysroots/aarch64-linux-gnu --with-sysroot=/home/<USER>/workspace/tcwg-make-release_1/_build/builds/destdir/x86_64-unknown-linux-gnu/aarch64-linux-gnu/libc --enable-checking=release --disable-bootstrap --enable-languages=c,c++,fortran,lto --build=x86_64-unknown-linux-gnu --host=x86_64-unknown-linux-gnu --target=aarch64-linux-gnu --prefix=/home/<USER>/workspace/tcwg-make-release_1/_build/builds/destdir/x86_64-unknown-linux-gnu
Thread model: posix
gcc version 6.5.0 (Linaro GCC 6.5-2018.12) 
configure:4080: $? = 0
configure:4069: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -V >&5
aarch64-linux-gnu-gcc: error: unrecognized command line option '-V'
aarch64-linux-gnu-gcc: fatal error: no input files
compilation terminated.
configure:4080: $? = 1
configure:4069: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -qversion >&5
aarch64-linux-gnu-gcc: error: unrecognized command line option '-qversion'; did you mean '--version'?
aarch64-linux-gnu-gcc: fatal error: no input files
compilation terminated.
configure:4080: $? = 1
configure:4069: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -version >&5
aarch64-linux-gnu-gcc: error: unrecognized command line option '-version'
aarch64-linux-gnu-gcc: fatal error: no input files
compilation terminated.
configure:4080: $? = 1
configure:4100: checking whether the C compiler works
configure:4122: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -static -I./bzip2-1.0.8  -static -L./bzip2-1.0.8 conftest.c  >&5
configure:4126: $? = 0
configure:4177: result: yes
configure:4181: checking for C compiler default output file name
configure:4183: result: a.out
configure:4189: checking for suffix of executables
configure:4196: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -o conftest -static -I./bzip2-1.0.8  -static -L./bzip2-1.0.8 conftest.c  >&5
configure:4200: $? = 0
configure:4224: result: 
configure:4248: checking whether we are cross compiling
configure:4256: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -o conftest -static -I./bzip2-1.0.8  -static -L./bzip2-1.0.8 conftest.c  >&5
configure:4260: $? = 0
configure:4267: ./conftest
./configure: line 4269: ./conftest: cannot execute binary file: Exec format error
configure:4271: $? = 126
configure:4286: result: yes
configure:4292: checking for suffix of object files
configure:4315: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4319: $? = 0
configure:4343: result: o
configure:4347: checking whether the compiler supports GNU C
configure:4367: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4367: $? = 0
configure:4379: result: yes
configure:4390: checking whether /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc accepts -g
configure:4411: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -g  conftest.c >&5
configure:4411: $? = 0
configure:4458: result: yes
configure:4478: checking for /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc option to enable C11 features
configure:4493: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc  -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4493: $? = 0
configure:4512: result: none needed
configure:4636: checking whether /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc understands -c and -o together
configure:4659: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c conftest.c -o conftest2.o
configure:4662: $? = 0
configure:4659: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c conftest.c -o conftest2.o
configure:4662: $? = 0
configure:4678: result: yes
configure:4698: checking whether make supports the include directive
configure:4713: make -f confmf.GNU && cat confinc.out
this is the am__doit target
configure:4716: $? = 0
configure:4735: result: yes (GNU style)
configure:4761: checking dependency style of /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc
configure:4874: result: gcc3
configure:4893: checking for BZ2_bzReadOpen in -lbz2
configure:4922: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -o conftest -static -I./bzip2-1.0.8  -static -L./bzip2-1.0.8 conftest.c -lbz2   >&5
configure:4922: $? = 0
configure:4934: result: yes
configure:4949: checking for stdio.h
configure:4949: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4949: $? = 0
configure:4949: result: yes
configure:4949: checking for stdlib.h
configure:4949: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4949: $? = 0
configure:4949: result: yes
configure:4949: checking for string.h
configure:4949: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4949: $? = 0
configure:4949: result: yes
configure:4949: checking for inttypes.h
configure:4949: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4949: $? = 0
configure:4949: result: yes
configure:4949: checking for stdint.h
configure:4949: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4949: $? = 0
configure:4949: result: yes
configure:4949: checking for strings.h
configure:4949: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4949: $? = 0
configure:4949: result: yes
configure:4949: checking for sys/stat.h
configure:4949: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4949: $? = 0
configure:4949: result: yes
configure:4949: checking for sys/types.h
configure:4949: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4949: $? = 0
configure:4949: result: yes
configure:4949: checking for unistd.h
configure:4949: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4949: $? = 0
configure:4949: result: yes
configure:4974: checking for fcntl.h
configure:4974: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4974: $? = 0
configure:4974: result: yes
configure:4980: checking for limits.h
configure:4980: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4980: $? = 0
configure:4980: result: yes
configure:4986: checking for stddef.h
configure:4986: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:4986: $? = 0
configure:4986: result: yes
configure:4992: checking for stdint.h
configure:4992: result: yes
configure:4998: checking for stdlib.h
configure:4998: result: yes
configure:5004: checking for string.h
configure:5004: result: yes
configure:5010: checking for unistd.h
configure:5010: result: yes
configure:5019: checking for int64_t
configure:5019: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:5019: $? = 0
configure:5019: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
conftest.c: In function 'main':
conftest.c:62:53: warning: integer overflow in expression [-Woverflow]
    < (int64_t) (((((int64_t) 1 << N) << N) - 1) * 2 + 2))];
                                                     ^
conftest.c:61:12: error: storage size of 'test_array' isn't constant
 static int test_array [1 - 2 * !((int64_t) (((((int64_t) 1 << N) << N) - 1) * 2 + 1)
            ^~~~~~~~~~
configure:5019: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "bsdiff"
| #define PACKAGE_TARNAME "bsdiff"
| #define PACKAGE_VERSION "0.1"
| #define PACKAGE_STRING "bsdiff 0.1"
| #define PACKAGE_BUGREPORT ""
| #define PACKAGE_URL ""
| #define PACKAGE "bsdiff"
| #define VERSION "0.1"
| #define HAVE_LIBBZ2 1
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define STDC_HEADERS 1
| #define HAVE_FCNTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| 	        enum { N = 64 / 2 - 1 };
| int
| main (void)
| {
| static int test_array [1 - 2 * !((int64_t) (((((int64_t) 1 << N) << N) - 1) * 2 + 1)
| 		 < (int64_t) (((((int64_t) 1 << N) << N) - 1) * 2 + 2))];
| test_array [0] = 0;
| return test_array [0];
| 
|   ;
|   return 0;
| }
configure:5019: result: yes
configure:5028: checking for off_t
configure:5028: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:5028: $? = 0
configure:5028: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
conftest.c: In function 'main':
conftest.c:60:20: error: expected expression before ')' token
 if (sizeof ((off_t)))
                    ^
configure:5028: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "bsdiff"
| #define PACKAGE_TARNAME "bsdiff"
| #define PACKAGE_VERSION "0.1"
| #define PACKAGE_STRING "bsdiff 0.1"
| #define PACKAGE_BUGREPORT ""
| #define PACKAGE_URL ""
| #define PACKAGE "bsdiff"
| #define VERSION "0.1"
| #define HAVE_LIBBZ2 1
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define STDC_HEADERS 1
| #define HAVE_FCNTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main (void)
| {
| if (sizeof ((off_t)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:5028: result: yes
configure:5039: checking for size_t
configure:5039: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:5039: $? = 0
configure:5039: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
conftest.c: In function 'main':
conftest.c:60:21: error: expected expression before ')' token
 if (sizeof ((size_t)))
                     ^
configure:5039: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "bsdiff"
| #define PACKAGE_TARNAME "bsdiff"
| #define PACKAGE_VERSION "0.1"
| #define PACKAGE_STRING "bsdiff 0.1"
| #define PACKAGE_BUGREPORT ""
| #define PACKAGE_URL ""
| #define PACKAGE "bsdiff"
| #define VERSION "0.1"
| #define HAVE_LIBBZ2 1
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define STDC_HEADERS 1
| #define HAVE_FCNTL_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main (void)
| {
| if (sizeof ((size_t)))
| 	    return 0;
|   ;
|   return 0;
| }
configure:5039: result: yes
configure:5050: checking for uint8_t
configure:5050: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -c -static -I./bzip2-1.0.8  conftest.c >&5
configure:5050: $? = 0
configure:5050: result: yes
configure:5070: checking build system type
configure:5086: result: x86_64-pc-linux-gnu
configure:5106: checking host system type
configure:5121: result: aarch64-unknown-linux-gnu
configure:5141: checking for GNU libc compatible malloc
configure:5187: result: yes
configure:5210: checking for memset
configure:5210: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -o conftest -static -I./bzip2-1.0.8  -static -L./bzip2-1.0.8 conftest.c -lbz2  >&5
conftest.c:46:6: warning: conflicting types for built-in function 'memset'
 char memset (void);
      ^~~~~~
configure:5210: $? = 0
configure:5210: result: yes
configure:5321: checking that generated files are newer than configure
configure:5327: result: done
configure:5362: creating ./config.status

## ---------------------- ##
## Running config.status. ##
## ---------------------- ##

This file was extended by bsdiff config.status 0.1, which was
generated by GNU Autoconf 2.72.  Invocation command line was

  CONFIG_FILES    = 
  CONFIG_HEADERS  = 
  CONFIG_LINKS    = 
  CONFIG_COMMANDS = 
  $ ./config.status 

on archlinuxg

config.status:869: creating Makefile
config.status:869: creating config.h
config.status:1050: config.h is unchanged
config.status:1098: executing depfiles commands
config.status:1175: cd .       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for 'am--depfiles'.
config.status:1180: $? = 0

## ---------------- ##
## Cache variables. ##
## ---------------- ##

ac_cv_build=x86_64-pc-linux-gnu
ac_cv_c_compiler_gnu=yes
ac_cv_c_int64_t=yes
ac_cv_c_uint8_t=yes
ac_cv_env_CC_set=set
ac_cv_env_CC_value=/home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc
ac_cv_env_CFLAGS_set=set
ac_cv_env_CFLAGS_value='-static -I./bzip2-1.0.8'
ac_cv_env_CPPFLAGS_set=
ac_cv_env_CPPFLAGS_value=
ac_cv_env_LDFLAGS_set=set
ac_cv_env_LDFLAGS_value='-static -L./bzip2-1.0.8'
ac_cv_env_LIBS_set=
ac_cv_env_LIBS_value=
ac_cv_env_build_alias_set=
ac_cv_env_build_alias_value=
ac_cv_env_host_alias_set=set
ac_cv_env_host_alias_value=aarch64-linux-gnu
ac_cv_env_target_alias_set=
ac_cv_env_target_alias_value=
ac_cv_func_malloc_0_nonnull=yes
ac_cv_func_memset=yes
ac_cv_header_fcntl_h=yes
ac_cv_header_inttypes_h=yes
ac_cv_header_limits_h=yes
ac_cv_header_stddef_h=yes
ac_cv_header_stdint_h=yes
ac_cv_header_stdio_h=yes
ac_cv_header_stdlib_h=yes
ac_cv_header_string_h=yes
ac_cv_header_strings_h=yes
ac_cv_header_sys_stat_h=yes
ac_cv_header_sys_types_h=yes
ac_cv_header_unistd_h=yes
ac_cv_host=aarch64-unknown-linux-gnu
ac_cv_lib_bz2_BZ2_bzReadOpen=yes
ac_cv_objext=o
ac_cv_path_install='/usr/bin/install -c'
ac_cv_path_mkdir=/usr/bin/mkdir
ac_cv_prog_AWK=gawk
ac_cv_prog_CC=/home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc
ac_cv_prog_ac_ct_STRIP=strip
ac_cv_prog_cc_c11=
ac_cv_prog_cc_g=yes
ac_cv_prog_cc_stdc=
ac_cv_prog_make_make_set=yes
ac_cv_type_off_t=yes
ac_cv_type_size_t=yes
am_cv_CC_dependencies_compiler_type=gcc3
am_cv_filesystem_timestamp_resolution=0.01
am_cv_make_support_nested_variables=yes
am_cv_prog_cc_c_o=yes
am_cv_prog_tar_ustar=gnutar
am_cv_sleep_fractional_seconds=yes
am_cv_xargs_n_works=yes

## ----------------- ##
## Output variables. ##
## ----------------- ##

ACLOCAL='${SHELL} '\''/home/<USER>/project/bsdiff/missing'\'' aclocal-1.18'
AMDEPBACKSLASH='\'
AMDEP_FALSE='#'
AMDEP_TRUE=''
AMTAR='$${TAR-tar}'
AM_BACKSLASH='\'
AM_DEFAULT_V='$(AM_DEFAULT_VERBOSITY)'
AM_DEFAULT_VERBOSITY='1'
AM_V='$(V)'
AUTOCONF='${SHELL} '\''/home/<USER>/project/bsdiff/missing'\'' autoconf'
AUTOHEADER='${SHELL} '\''/home/<USER>/project/bsdiff/missing'\'' autoheader'
AUTOMAKE='${SHELL} '\''/home/<USER>/project/bsdiff/missing'\'' automake-1.18'
AWK='gawk'
CC='/home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc'
CCDEPMODE='depmode=gcc3'
CFLAGS='-static -I./bzip2-1.0.8'
CPPFLAGS=''
CSCOPE='cscope'
CTAGS='ctags'
CYGPATH_W='echo'
DEFS='-DHAVE_CONFIG_H'
DEPDIR='.deps'
ECHO_C=''
ECHO_N='-n'
ECHO_T=''
ETAGS='etags'
EXEEXT=''
INSTALL_DATA='${INSTALL} -m 644'
INSTALL_PROGRAM='${INSTALL}'
INSTALL_SCRIPT='${INSTALL}'
INSTALL_STRIP_PROGRAM='$(install_sh) -c -s'
LDFLAGS='-static -L./bzip2-1.0.8'
LIBOBJS=''
LIBS='-lbz2 '
LTLIBOBJS=''
MAKEINFO='${SHELL} '\''/home/<USER>/project/bsdiff/missing'\'' makeinfo'
MKDIR_P='/usr/bin/mkdir -p'
OBJEXT='o'
PACKAGE='bsdiff'
PACKAGE_BUGREPORT=''
PACKAGE_NAME='bsdiff'
PACKAGE_STRING='bsdiff 0.1'
PACKAGE_TARNAME='bsdiff'
PACKAGE_URL=''
PACKAGE_VERSION='0.1'
PATH_SEPARATOR=':'
SET_MAKE=''
SHELL='/bin/sh'
STRIP='strip'
VERSION='0.1'
ac_ct_CC=''
am__EXEEXT_FALSE=''
am__EXEEXT_TRUE='#'
am__fastdepCC_FALSE='#'
am__fastdepCC_TRUE=''
am__include='include'
am__isrc=''
am__leading_dot='.'
am__nodep='_no'
am__quote=''
am__rm_f_notfound=''
am__tar='tar --format=ustar -chf - "$$tardir"'
am__untar='tar -xf -'
am__xargs_n='xargs -n'
bindir='${exec_prefix}/bin'
build='x86_64-pc-linux-gnu'
build_alias=''
build_cpu='x86_64'
build_os='linux-gnu'
build_vendor='pc'
datadir='${datarootdir}'
datarootdir='${prefix}/share'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
dvidir='${docdir}'
exec_prefix='${prefix}'
host='aarch64-unknown-linux-gnu'
host_alias='aarch64-linux-gnu'
host_cpu='aarch64'
host_os='linux-gnu'
host_vendor='unknown'
htmldir='${docdir}'
includedir='${prefix}/include'
infodir='${datarootdir}/info'
install_sh='${SHELL} /home/<USER>/project/bsdiff/install-sh'
libdir='${exec_prefix}/lib'
libexecdir='${exec_prefix}/libexec'
localedir='${datarootdir}/locale'
localstatedir='${prefix}/var'
mandir='${datarootdir}/man'
mkdir_p='$(MKDIR_P)'
oldincludedir='/usr/include'
pdfdir='${docdir}'
prefix='/usr/local'
program_transform_name='s,x,x,'
psdir='${docdir}'
runstatedir='${localstatedir}/run'
sbindir='${exec_prefix}/sbin'
sharedstatedir='${prefix}/com'
sysconfdir='${prefix}/etc'
target_alias=''

## ----------- ##
## confdefs.h. ##
## ----------- ##

/* confdefs.h */
#define PACKAGE_NAME "bsdiff"
#define PACKAGE_TARNAME "bsdiff"
#define PACKAGE_VERSION "0.1"
#define PACKAGE_STRING "bsdiff 0.1"
#define PACKAGE_BUGREPORT ""
#define PACKAGE_URL ""
#define PACKAGE "bsdiff"
#define VERSION "0.1"
#define HAVE_LIBBZ2 1
#define HAVE_STDIO_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_STDINT_H 1
#define HAVE_STRINGS_H 1
#define HAVE_SYS_STAT_H 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_UNISTD_H 1
#define STDC_HEADERS 1
#define HAVE_FCNTL_H 1
#define HAVE_LIMITS_H 1
#define HAVE_STDDEF_H 1
#define HAVE_STDINT_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_UNISTD_H 1
#define HAVE_MALLOC 1
#define HAVE_MEMSET 1

configure: exit 0
