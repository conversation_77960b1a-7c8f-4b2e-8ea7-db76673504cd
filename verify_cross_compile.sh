#!/bin/bash

echo "=== bsdiff 交叉编译验证报告 ==="
echo "编译器: /home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc"
echo "目标架构: ARM aarch64"
echo "编译时间: $(date)"
echo ""

echo "=== 二进制文件信息 ==="
echo "bsdiff:"
file bsdiff
echo ""
echo "bspatch:"
file bspatch
echo ""

echo "=== 文件大小 ==="
ls -lh bsdiff bspatch
echo ""

echo "=== 架构验证 ==="
/home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-readelf -h bsdiff | grep -E "(Class|Machine|Type)"
echo ""

echo "=== 静态链接验证 ==="
echo "检查动态库依赖 (应该为空):"
/home/<USER>/project/d2v/platform/D2V/build/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-objdump -p bsdiff | grep NEEDED || echo "无动态库依赖 - 静态链接成功!"
echo ""

echo "=== 编译配置信息 ==="
echo "使用的bzip2库: ./bzip2-1.0.8/libbz2.a"
echo "编译标志: -static -I./bzip2-1.0.8"
echo "链接标志: -static -L./bzip2-1.0.8"
echo ""

echo "=== 总结 ==="
echo "✓ 成功使用交叉编译器编译bsdiff"
echo "✓ 目标架构: ARM aarch64"
echo "✓ 静态链接，无外部依赖"
echo "✓ 可在aarch64 Linux系统上运行"
